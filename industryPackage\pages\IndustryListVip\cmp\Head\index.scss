.head {
	position: relative;

	// 顶部渐变背景
	.top-bg {
		position: relative;
		height: 360rpx;
		width: 100%;
		background: linear-gradient(180deg, #ffe5bf 0%, #ffffff 64%);
		height: 576rpx;
		padding-top: 228rpx;
		box-sizing: border-box;
		// 背景图片
		.bg-image {
			position: absolute;
			top: 64rpx;
			right: 0;
			width: 280rpx;
			height: 280rpx;
			// border: 1px solid red;
		}
	}

	// 内容卡片
	.content-card {
		width: 750rpx;
		height: 348rpx;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		box-sizing: border-box;
		padding: 24rpx;
		box-shadow: 0rpx -8rpx 24rpx 0rpx rgba(245, 231, 211, 0.5);
		// box-shadow: 0rpx -24rpx 80rpx 0rpx rgba(245, 231, 211, 0.5) !important;
		backdrop-filter: blur(4px); /* 模糊背后的内容 */
		-webkit-backdrop-filter: blur(4px);
		background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 50%, #ffffff 100%);
		// border: 1px solid red;

		// 标题
		.title {
			font-weight: 600;
			font-size: 32rpx;
			color: #20263a;
			margin-bottom: 24rpx;
		}

		// 统计数据容器
		.stats-container {
			display: flex;
			justify-content: space-between;
			margin-bottom: 28rpx;

			.stats-item {
				text-align: center;
				width: 226rpx;
				height: 124rpx;
				background: linear-gradient(180deg, #fff5e5 0%, #ffffff 50%), #ffffff;
				border-radius: 8rpx;
				border: 1rpx solid #fcdeb3;
				box-sizing: border-box;
				padding-top: 24rpx;

				.stats-value {
					font-weight: 600;
					font-size: 32rpx;
					color: #525665;
					margin-bottom: 8rpx;

					.stats-unit {
						font-weight: 400;
						font-size: 24rpx;
						color: #9b9eac;
						margin-left: 4rpx;
					}
				}

				.stats-label {
					font-weight: 400;
					font-size: 24rpx;
					color: #ceab95;
				}
			}
		}

		// 按钮
		.action-button {
			width: 702rpx;
			height: 72rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			border: 1rpx solid #e72410;
			font-weight: 400;
			font-size: 32rpx;
			color: #e72410;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	// margin-bottom: -116rpx !important;
}

.masonry {
	.bg-image {
		width: 200rpx;
		height: 200rpx;
		right: 4rpx;
		top: 110rpx;
		z-index: 0;
	}

	.top-bg {
		background: linear-gradient(180deg, #d8caf9 0%, #ffffff 65%) !important;
	}

	.content-card {
		border: none;
		box-shadow: 0rpx -8rpx 24rpx 0rpx rgba(221, 211, 245, 0.5) !important;
		backdrop-filter: blur(10px); /* 模糊背后的内容 */
		-webkit-backdrop-filter: blur(10px);

		.stats-container {
			.stats-item {
				width: 166rpx;
				background: linear-gradient(180deg, #f3eeff 0%, #ffffff 50%), #ffffff !important;
				border: 1rpx solid #e4d1ff;

				.stats-label {
					color: #ae9ed7;
				}
			}
		}
	}
}
