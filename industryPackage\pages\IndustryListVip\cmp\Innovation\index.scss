.wrap {
  .pie {
    width: 100%;
    height: 536rpx;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; // 防止放大时超出容器
  }

  // 饼图包装器
  .pie-wrapper {
    position: relative;
    transition: all 0.3s ease;
    transform-origin: left center; // 设置变换原点为左侧中心

    // 当有选中项时，只在水平方向放大
    &.has-selection {
      transform: scaleX(1.1); // 只在 X 轴方向放大
    }
  }

  .list {
    background: #fff;
    padding: 0 24rpx 24rpx;

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 76rpx;
      padding: 0 20rpx;
      margin-bottom: 8rpx;
      background: #ffffff;
      border-radius: 8rpx;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      // 普通状态
      .left-content {
        display: flex;
        align-items: center;

        .color-dot {
          width: 24rpx;
          height: 24rpx;
          border-radius: 50%;
          margin-right: 8rpx;
        }

        .name {
          font-weight: 400;
          font-size: 24rpx;
          color: #74798c;
        }
      }

      .right-content {
        display: flex;
        align-items: center;

        .percentage {
          font-size: 28rpx;
          color: #525665;
        }

        .count {
          width: 200rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #525665;
          text-align: right;
        }
      }

      // 选中状态
      &.selected {
        background: #f7f7f7;

        .left-content {
          .name {
            color: #20263a;
          }
        }

        .right-content {
          .percentage {
            font-weight: 600 !important;
            font-size: 28rpx !important;
            color: #20263a !important;
          }

          .count {
            font-weight: 600 !important;
            font-size: 28rpx !important;
            color: #20263a !important;
          }
        }
      }
    }
  }
}
