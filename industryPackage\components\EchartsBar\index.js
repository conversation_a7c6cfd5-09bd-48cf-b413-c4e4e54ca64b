var echarts = require('../ec-canvas/echarts');
import {getPx} from '../../../utils/formate';
Component({
  properties: {
    // 图表数据
    chartData: {
      type: Array,
      value: [
        // {year: '2019', value: 120},
        // {year: '2020', value: 150},
        // {year: '2021', value: 180},
        // {year: '2022', value: 200},
        // {year: '2023', value: 230}
      ],
      observer: 'onDataChange'
    },
    // 柱状图颜色 (渐变色)
    barColor: {
      type: Array,
      value: ['#30CEAE', '#29B296']
    },
    // Y轴标题 (单位)
    yAxisTitle: {
      type: String,
      value: '单位：家'
    },
    // 柱子宽度 (会根据数据量自动调整)
    barWidth: {
      type: Number,
      value: 40
    },
    isRepeat: {
      // 一个页面同时用一个组件的时候 会导致第一个不显示 --解决办法 这里智能解决一个页面用2次的清空
      type: Boolean,
      value: false
    },
    // 空状态显示文本
    emptyText: {
      type: String,
      value: '-暂无内容-'
    }
  },

  data: {
    ec: {},
    forceUseOldCanvas: false
    // myChart: null // 每个组件实例的私有图表实例
  },

  lifetimes: {
    attached() {
      // 检测开发者工具
      wx.getSystemInfo({
        success: res =>
          res.platform == 'devtools' &&
          this.setData({
            forceUseOldCanvas: true
          })
      });

      // 初始化 ec 对象
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      });
    },
    // 组件销毁的时候
    detached() {
      // 销毁图表实例
      if (this.barChart) {
        this.barChart.dispose();
        this.barChart = null;
      }
      if (this.barChart1) {
        this.barChart1.dispose();
        this.barChart1 = null;
      }

      // 清理待处理的数据
      this.pendingData = null;
    }
  },

  methods: {
    // 数据变化时重新渲染图表
    onDataChange(newData) {
      // 无论数据是否为空都要更新图表（空数据时显示暂无内容）
      setTimeout(() => {
        const chartInstance = this.properties.isRepeat
          ? this.barChart1
          : this.barChart;

        console.log('EchartsBar onDataChange:', newData, chartInstance);
        if (chartInstance) {
          this.updateChart(newData); // 直接传递新数据
        } else {
          console.log('图表实例为null，等待初始化完成');
          // 保存数据，等初始化完成后使用
          this.pendingData = newData;
        }
      }, 100);
    },

    // 初始化图表
    initChart(canvas, width, height, dpr) {
      const chartInstance = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      // 将图表实例保存到组件实例中
      if (this.properties.isRepeat) {
        this.barChart1 = chartInstance;
      } else {
        this.barChart = chartInstance;
      }

      // 检查是否有待处理的数据
      if (this.pendingData) {
        console.log('使用待处理的数据更新图表:', this.pendingData);
        this.updateChart(this.pendingData);
        this.pendingData = null; // 清除待处理的数据
      } else {
        this.updateChart();
      }

      return chartInstance;
    },

    // 更新图表
    updateChart(chartData) {
      // 如果没有传入数据，则使用 properties 中的数据
      const data = chartData || this.properties.chartData;
      let {barColor, yAxisTitle, barWidth} = this.properties;

      // 检查图表实例是否存在
      const chartInstance = this.properties.isRepeat
        ? this.barChart1
        : this.barChart;
      if (!chartInstance) return;

      // 处理空数据情况
      if (!data || data.length === 0) {
        this.showEmptyState(chartInstance);
        return;
      }

      // 隐藏loading状态（如果之前显示了的话）
      chartInstance.hideLoading();

      // 计算柱子宽度 - 根据数据量自动调整
      const calculatedBarWidth = Math.min(
        barWidth,
        Math.floor(300 / data.length)
      );
      let yTitle = '';
      const maxValue = Math.max(...data.map(item => item.value));
      if (maxValue >= 100000) {
        yTitle = yAxisTitle.replace('单位：', '单位：万');
      } else {
        yTitle = yAxisTitle.replace('单位：', '单位：');
      }

      const option = {
        // 清除空状态的 graphic 元素
        graphic: {
          elements: []
        },
        // 添加tooltip配置
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: '#ccc',
          borderWidth: 1,
          borderRadius: 4,
          textStyle: {
            color: '#333',
            fontSize: getPx(24)
          },
          padding: [8, 12],
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);',
          formatter: function (params) {
            const param = Array.isArray(params) ? params[0] : params;
            let value = param.value;
            let displayValue = value;
            let unit = '';

            // 根据数值大小显示合适的单位
            if (maxValue >= 100000) {
              displayValue = (value / 10000).toFixed(2);
              unit = yAxisTitle.replace('单位：', '万');
            } else {
              unit = yAxisTitle.replace('单位：', '');
            }

            // 使用富文本样式
            return `{title|${param.name}}\n{marker|${param.marker}}{value|${displayValue}}{unit|${unit}}`;
          },
          // 富文本样式配置
          rich: {
            title: {
              color: '#333',
              fontSize: getPx(28),
              fontWeight: 'bold',
              lineHeight: getPx(40)
            },
            marker: {
              fontSize: getPx(24)
            },
            value: {
              color: '#1890ff',
              fontSize: getPx(26),
              fontWeight: 'bold'
            },
            unit: {
              color: '#666',
              fontSize: getPx(22),
              fontStyle: 'italic'
            }
          },
          // 设置为点击触发
          // triggerOn: 'click',
          // 显示延迟
          showDelay: 0,
          // 隐藏延迟
          hideDelay: 1000
        },
        grid: {
          left: '8%',
          right: '18',
          top: '14%',
          bottom: '15',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.year),
          axisLabel: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#74798C'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: yTitle,
          nameTextStyle: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#74798C',
            padding: [0, 7, 0, 0],
            align: 'right' // 靠右对齐
          },
          axisLabel: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#74798C',
            formatter: function (value, index) {
              // if (index === 0) {
              //   return yTitle; // 第一个刻度显示标题
              // }
              if (maxValue >= 100000) {
                return (value / 10000).toFixed(0);
              } else if (maxValue >= 10000) {
                return (value / 1000).toFixed(0); // 添加k单位标识
              }
              return value;
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#F5F5F5',
              width: 1,
              type: 'dashed'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: data.map(item => item.value),
            barWidth: getPx(calculatedBarWidth),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor[0] || '#30CEAE'
                  },
                  {
                    offset: 1,
                    color: barColor[1] || '#29B296'
                  }
                ]
              }
            },
            // 显示柱状图上面的数字标签
            label: {
              show: true,
              position: 'top',
              fontWeight: 400,
              fontSize: getPx(24),
              color: '#20263A',
              formatter: '{c}'
            }
          }
        ]
      };

      // 使用组件实例的图表对象
      // 使用 true 参数完全替换配置，确保清除空状态
      chartInstance.setOption(option, true);
    },

    // 显示空状态
    showEmptyState(chartInstance) {
      const {emptyText} = this.properties;

      // 使用 graphic 组件显示空状态
      const emptyOption = {
        // 完全清除坐标轴
        xAxis: {show: false},
        yAxis: {show: false},
        // 清除所有系列数据
        series: [],
        // 清除网格
        grid: {show: false},
        // 清除标题
        title: null,
        // 清除图例
        legend: {show: false},
        // 显示空状态文本
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'middle',
              style: {
                text: emptyText,
                fontSize: 14,
                fontWeight: 'normal',
                fill: '#999999'
              }
            }
          ]
        }
      };
      // 使用 true 参数，表示不合并配置，完全替换
      chartInstance.setOption(emptyOption, true);
    }
  }
});
