var echarts = require('../ec-canvas/echarts');

let myChart = null;

Component({
  properties: {
    // 饼图数据
    pieData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    // 饼图颜色
    pieColors: {
      type: Array,
      value: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    },
    // 内圆半径
    innerRadius: {
      type: String,
      value: '65%'
    },
    // 外圆半径
    outerRadius: {
      type: String,
      value: '85%'
    },
    // 外部控制的选中索引
    selectedIndex: {
      type: Number,
      value: 0,
      observer: 'onSelectedIndexChange'
    }
  },

  data: {
    ec: {},
    forceUseOldCanvas: false,
    centerPercentage: '0.00%',
    currentIndex: 0, // 当前选中的数据索引
    displayTitle: '', // 当前显示的标题
    displayPercentage: '0.00%' // 当前显示的百分比
  },

  lifetimes: {
    attached() {
      // 检测开发者工具
      wx.getSystemInfo({
        success: res =>
          res.platform == 'devtools' &&
          this.setData({
            forceUseOldCanvas: true
          })
      });

      // 初始化 ec 对象
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      });
    }
  },

  methods: {
    // 数据变化时重新渲染图表
    onDataChange(newData) {
      if (newData && newData.length > 0) {
        this.setData({
          currentIndex: this.properties.selectedIndex || 0 // 使用外部传入的索引或默认为0
        });
        this.updateDisplayData();
        if (myChart) {
          this.updateChart();
        }
      }
    },

    // 外部选中索引变化时的处理
    onSelectedIndexChange(newIndex) {
      if (newIndex !== this.data.currentIndex) {
        this.setData({
          currentIndex: newIndex
        });
        this.updateDisplayData();
        if (myChart) {
          this.updateChart();
        }
      }
    },

    // 更新中心显示的数据
    updateDisplayData() {
      const {pieData} = this.properties;
      const {currentIndex} = this.data;

      if (!pieData || pieData.length === 0) return;

      const currentItem = pieData[currentIndex] || pieData[0];

      // 直接使用数据中的percentage字段
      const percentage = currentItem.percentage || '0.00';

      this.setData({
        displayTitle: currentItem.name,
        displayPercentage: `${percentage}` // `${percentage}%`
      });
    },

    // 处理饼图点击事件
    onPieClick(params) {
      const {pieData} = this.properties;
      if (!pieData || pieData.length === 0) return;

      // 找到点击的数据索引
      const clickedIndex = pieData.findIndex(item => item.name === params.name);

      if (clickedIndex !== -1) {
        this.setData({
          currentIndex: clickedIndex
        });
        this.updateDisplayData();
        this.updateChart(); // 重新渲染图表以更新选中状态

        // 触发事件通知外层组件更新列表状态
        this.triggerEvent('pieClick', {
          index: clickedIndex,
          data: pieData[clickedIndex]
        });
      }
    },

    // 初始化图表
    initChart(canvas, width, height, dpr) {
      myChart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      // 添加点击事件监听
      myChart.on('click', params => {
        this.onPieClick(params);
      });

      this.updateChart();
      this.updateDisplayData(); // 初始化显示第一个数据
      return myChart;
    },

    // 更新图表
    updateChart() {
      const {pieData, pieColors, innerRadius, outerRadius} = this.properties;
      const {currentIndex} = this.data;

      if (!pieData || pieData.length === 0) return;

      // 计算选中时的外圆半径（增加宽度）
      const baseOuterRadius = parseInt(outerRadius.replace('%', ''));
      const selectedOuterRadius = `${baseOuterRadius + 8}%`; // 选中时外圆半径增加8%

      // 基础饼图数据（所有扇形）
      const baseData = pieData.map((item, index) => ({
        ...item,
        selected: false
      }));

      // 选中扇形的数据（只显示选中的那一个）
      const selectedData = pieData.map((item, index) => ({
        ...item,
        value: index === currentIndex ? item.value : 0, // 只有选中的扇形有值
        selected: false
      }));

      const option = {
        color: pieColors,
        series: [
          // 基础饼图系列（正常大小）
          {
            type: 'pie',
            radius: [innerRadius, outerRadius],
            center: ['50%', '50%'],
            data: baseData,
            selectedMode: false, // 禁用选中模式
            selectedOffset: 0,
            z: 1, // 层级较低

            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {},
            emphasis: {
              itemStyle: {
                shadowBlur: 0,
                shadowOffsetX: 0,
                shadowColor: 'transparent'
              }
            }
          },
          // 选中扇形系列（更大半径）
          {
            type: 'pie',
            radius: [innerRadius, selectedOuterRadius],
            center: ['50%', '50%'],
            data: selectedData,
            selectedMode: false,
            selectedOffset: 0,
            z: 2, // 层级较高，覆盖在基础饼图上

            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {},
            emphasis: {
              itemStyle: {
                shadowBlur: 0,
                shadowOffsetX: 0,
                shadowColor: 'transparent'
              }
            }
          }
        ]
      };

      myChart.setOption(option);
    }
  }
});
