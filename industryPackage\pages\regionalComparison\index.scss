.content {
  min-height: 100vh;
  background: #f7f7f7;

  .head {
    width: 100vw;
    height: 810rpx;
    position: relative;
    .img1 {
      width: 100%;
      height: 100%;
    }
    .img2 {
      position: absolute;
      right: 0;
      top: 106rpx;
      width: 300rpx;
      height: 300rpx;
    }
    .txt {
      position: absolute;
      left: 32rpx;
      top: 220rpx;
      font-weight: 600;
      font-size: 44rpx;
      background: linear-gradient(90deg, #421208 18%, rgba(66, 18, 8, 0.8) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      width: 380rpx;
      line-height: 60rpx;
      &::after {
        content: "";
        position: absolute;
        bottom: -6rpx;
        left: 0;
        width: 278rpx;
        height: 16rpx;
        background: linear-gradient(90deg, rgba(254, 150, 150, 0.56) 0%, rgba(255, 255, 255, 0.1) 100%);
      }
    }
    margin-bottom: -406rpx;
  }
  .wraps {
    width: 750rpx;
    position: relative;
    padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
    &_bg {
      position: absolute;
      top: 0rpx;
      display: block;
      width: 750rpx;
      height: 460rpx !important;
      height: auto;
      background: linear-gradient(346deg, rgba(247, 247, 247, 0) 0%, rgba(255, 255, 255, 0.5) 50%, #ffffff 100%);
      box-shadow: 0rpx -6rpx 16rpx 0rpx rgba(227, 186, 186, 0.13);
      backdrop-filter: blur(20rpx);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 24rpx 24rpx 0rpx 0rpx;
      overflow: hidden;
    }
    background: linear-gradient(346deg, rgba(247, 247, 247, 0) 0%, rgba(255, 255, 255, 0.5) 50%, #ffffff 100%);
    &_cont {
      position: relative;
      z-index: 1;

      .tabs {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 14rpx 8rpx 42rpx;
        box-sizing: border-box;

        .tab-item {
          flex: 1;
          text-align: center;
          font-weight: 600;
          height: 76rpx;
          font-size: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 400;
          font-size: 32rpx;
          color: #525665;
          position: relative;

          &.active {
            font-weight: 500;
            font-size: 32rpx;
            color: #20263a;

            &::after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 62rpx;
              height: 6rpx;
              background: linear-gradient(90deg, #f56e60 0%, #e72410 100%);
              border-radius: 14rpx 14rpx 0rpx 0rpx;
            }
          }
        }
      }
    }
  }

  // 表单区域
  .ipt_wraps {
    padding: 0 24rpx 0;
    position: relative;
    .add-item {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      .ipt-item {
        display: flex;
        align-items: center;
        flex: 1;
        background: #fff;
        border-radius: 12rpx;
        height: 96rpx;
        overflow: hidden;

        .add-item-r {
          flex: 1;
          padding: 0 24rpx;
          color: #20263a;
          font-weight: 400;
          font-size: 32rpx;
          height: 96rpx;

          &.pla {
            color: #9b9eac;
          }
        }
        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
          margin-right: 24rpx;
        }
      }

      .delete-btn {
        flex-shrink: 0;
        width: 72rpx;
        height: 96rpx;
        background: #fff;
        border-radius: 12rpx;
        margin-left: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 26rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #e72410;
      margin-bottom: 54rpx;

      .add-icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 8rpx;
      }

      &.disabled {
        color: #9b9eac;
        background: transparent;
      }
    }
  }

  // 历史记录
  .history {
    width: 686rpx;
    background: #f2f2f2;
    border-radius: 16rpx;
    margin: 0 auto;
    padding: 24rpx 32rpx;
    padding-bottom: 0rpx;

    .history-title {
      margin-bottom: 40rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      text {
        font-weight: 400;
        font-size: 32rpx;
        color: #74798c;
      }
      .his_title_icon {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }

    .history-list {
      .history-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 0;
        margin-bottom: 16rpx;

        .history-name {
          font-weight: 400;
          font-size: 28rpx;
          color: #74798c;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          width: 580rpx;
        }

        image {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
  }

  // 对比按钮容器
  .compare-btn-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); // 适配刘海屏
    background: transparent;
    z-index: 1;

    .compare_btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(90deg, #f56e60 0%, #e72410 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(231, 36, 16, 0.3);
    }
  }
}
