<view class="wrap">
  <!-- 饼图 -->
  <view class="pie">
    <view class="pie-wrapper {{selectedIndex !== null ? 'has-selection' : ''}}">
      <EchartsPie
        pieData="{{pieData}}"
        pieColors="{{pieColors}}"
        selectedIndex="{{selectedIndex}}"
        bindpieClick="onPieClick"
      />
    </view>
  </view>
  <!-- 列表 -->
  <view class="list">
    <view
      class="item {{selectedIndex === index ? 'selected' : ''}}"
      wx:for="{{pieData}}"
      wx:key="index"
      bindtap="onItemClick"
      data-index="{{index}}"
    >
      <view class="left-content">
        <!-- 圆点 -->
        <view
          class="color-dot"
          style="background-color: {{pieColors[index]}};"
        ></view>
        <view class="name">{{item.name}}</view>
      </view>
      <view class="right-content">
        <view class="percentage">{{item.percentage}}</view>
        <view class="count">{{item.num}}件</view>
      </view>
    </view>
  </view>
  <!-- 后续来补充具体渲染逻辑  -- 柱状图 后面看是否用slot     -->
  <EchartsBar
    barColor="{{barColors}}"
    chartData="{{barData}}"
    isRepeat="{{true}}"
    wx:if="{{barData.length && isShowBar}}"
    yAxisTitle="单位：件"
  />
</view>
