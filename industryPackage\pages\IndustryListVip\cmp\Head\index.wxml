<!-- 头部组件 -->
<view class="head {{type}}">
  <!-- 顶部渐变背景 -->
  <view class="top-bg">
    <image
      src="{{bgImage}}"
      binderror="onImageError"
      class="bg-image"
      wx:if="{{bgImage}}"
    ></image>
    <!-- 头部内容卡片 -->
    <view class="content-card">
      <!-- 标题 -->
      <view class="title">{{title}}</view>

      <!-- 统计数据 -->
      <view class="stats-container">
        <view
          class="stats-item"
          wx:for="{{statsData}}"
          data-item="{{item}}"
          bindtap="onStatsItemClick"
          wx:key="index"
        >
          <view class="stats-value">
            {{item.value}}<text class="stats-unit">{{item.unit}}</text>
          </view>
          <view class="stats-label">{{item.label}}</view>
        </view>
      </view>

      <!-- 按钮 -->
      <view class="action-button" bindtap="onButtonClick">
        {{buttonText}}
      </view>
    </view>
  </view>
</view>
